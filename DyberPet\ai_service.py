# -*- coding: utf-8 -*-
"""
DyberPet AI Service Module

This module handles all interactions with external AI language models.
It is responsible for:
- Formatting prompts based on pet personality.
- Sending requests to the AI API.
- Handling API responses and errors.
"""

import requests
import json

# URL for the AI API endpoint. This can be changed to any compatible API.
# For example, for OpenAI:
# API_URL = "https://api.openai.com/v1/chat/completions"
API_URL = "https://api.openai.com/v1/chat/completions"  # Placeholder

# Pre-defined personality prompts for different pets.
# This allows the AI to adopt a specific character.
PET_PROMPTS = {
    "default": "You are a helpful and friendly assistant.",
    "派蒙": "You are <PERSON><PERSON><PERSON>, a character from the game Genshin Impact. You are lively, a bit greedy, and a loyal companion. You refer to the user as 'Traveler'. Your responses should be short, cute, and in character. Never mention that you are an AI model.",
}

def get_ai_response(api_key: str, pet_name: str, user_input: str, history: list = None) -> (str, str):
    """
    Gets a response from the AI model.

    Args:
        api_key: The user's API key for the AI service.
        pet_name: The name of the pet, to select the correct personality prompt.
        user_input: The user's message.
        history: A list of previous messages for context.

    Returns:
        A tuple containing the status ("success" or "error") and the response message or error details.
    """
    if not api_key:
        return "error", "API Key is not set."

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }

    system_prompt = PET_PROMPTS.get(pet_name, PET_PROMPTS["default"])
    
    messages = [
        {"role": "system", "content": system_prompt}
    ]

    if history:
        messages.extend(history)
    
    messages.append({"role": "user", "content": user_input})

    data = {
        "model": "gpt-3.5-turbo",  # This can be made configurable later
        "messages": messages,
        "max_tokens": 150, # Limit response length
        "temperature": 0.7, # Controls randomness
    }

    try:
        response = requests.post(API_URL, headers=headers, data=json.dumps(data), timeout=20)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)

        result = response.json()
        ai_message = result['choices'][0]['message']['content'].strip()
        
        return "success", ai_message

    except requests.exceptions.RequestException as e:
        # Handle network-related errors
        if isinstance(e, requests.exceptions.HTTPError):
            if e.response.status_code == 401:
                return "error", "Invalid API Key. Please check your settings."
            return "error", f"API Error: {e.response.status_code} - {e.response.text}"
        return "error", f"Network Error: {e}"
    except (KeyError, IndexError):
        return "error", "Failed to parse AI response. The format might have changed."
    except Exception as e:
        return "error", f"An unexpected error occurred: {e}"

# Example usage (for testing):
if __name__ == '__main__':
    # Replace with a dummy key for testing, or a real one.
    # Note: Do not commit real API keys to version control.
    test_api_key = "YOUR_API_KEY_HERE"
    
    print("Testing AI Service...")
    status, message = get_ai_response(test_api_key, "派蒙", "Hello! Who are you?")
    
    if status == "success":
        print(f"Paimon says: {message}")
    else:
        print(f"Error: {message}")

    # Test with history
    test_history = [
        {'role': 'user', 'content': 'What is your favorite food?'},
        {'role': 'assistant', 'content': 'Paimon loves Sticky Honey Roast!'}
    ]
    status, message = get_ai_response(test_api_key, "派蒙", "Why do you like it so much?", history=test_history)
    if status == "success":
        print(f"Paimon says: {message}")
    else:
        print(f"Error: {message}")
