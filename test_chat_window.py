#!/usr/bin/env python3
"""
测试ChatWindow的独立脚本
"""

import sys
import os

# 添加DyberPet目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'DyberPet'))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    
    # 模拟settings模块
    class MockSettings:
        def __init__(self):
            self.platform = 'win32'
            self.api_key = 'test_key'
    
    import DyberPet.settings as settings
    settings.platform = 'win32'
    settings.api_key = 'test_key'
    
    from DyberPet.extra_windows import ChatWindow
    
    def test_chat_window():
        app = QApplication(sys.argv)
        
        # 创建聊天窗口
        chat_window = ChatWindow()
        
        # 连接信号
        def on_send_message(message):
            print(f"发送消息: {message}")
            # 模拟AI回复
            QTimer.singleShot(1000, lambda: chat_window.update_chat_history(f"AI: 你好！你说了：{message}"))
        
        chat_window.send_message.connect(on_send_message)
        
        # 显示窗口
        chat_window.show()
        chat_window.move(100, 100)
        
        # 添加一些测试消息
        QTimer.singleShot(2000, lambda: chat_window.update_chat_history("You: 这是一条测试消息"))
        QTimer.singleShot(3000, lambda: chat_window.update_chat_history("AI: 这是AI的回复消息，测试样式是否正常显示"))
        
        print("ChatWindow测试启动成功！")
        print("- 窗口应该显示为400x350大小")
        print("- 有输入框和发送按钮")
        print("- 消息应该有不同的样式（用户消息蓝色，AI消息绿色）")
        print("- 可以拖动窗口")
        print("- 点击×按钮可以关闭")
        
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(test_chat_window())
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PySide6:")
    print("pip install PySide6==6.5.2")
    print("pip install PySide6-Fluent-Widgets==1.5.4")
    sys.exit(1)
except Exception as e:
    print(f"其他错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
