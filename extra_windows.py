
# -*- coding: utf-8 -*-
from PySide6.QtWidgets import QDialog, QVBoxLayout, QLineEdit, QPushButton
from PySide6.QtCore import Signal

class ChatWindow(QDialog):
    send_message = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Chat")
        self.layout = QVBoxLayout(self)
        self.input_box = QLineEdit(self)
        self.input_box.setPlaceholderText("Type your message...")
        self.send_button = QPushButton("Send", self)

        self.layout.addWidget(self.input_box)
        self.layout.addWidget(self.send_button)

        self.send_button.clicked.connect(self.send)
        self.input_box.returnPressed.connect(self.send)

    def send(self):
        message = self.input_box.text()
        if message:
            self.send_message.emit(message)
            self.input_box.clear()
            self.close()
